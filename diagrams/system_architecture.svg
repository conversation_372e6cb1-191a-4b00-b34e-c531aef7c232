
<svg width="1000" height="640" viewBox="0 0 1000 640" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="1000" height="640" fill="#FFFFFF"/>
  <!-- Devi<PERSON> -->
  <rect x="50" y="60" width="900" height="500" fill="none" stroke="#28a745" stroke-width="3" stroke-dasharray="10,5" rx="15"/>
  <text x="70" y="85" font-size="14" font-weight="bold" fill="#28a745">DEVICE SECURITY BOUNDARY</text>
  <!-- User Interface Layer -->
  <rect x="100" y="110" width="810" height="80" fill="#61dafb" fill-opacity="0.2" stroke="#0056b3" stroke-width="2" rx="10"/>
  <text x="505" y="140" text-anchor="middle" font-size="18" font-weight="bold">React Native Application</text>
  <text x="505" y="165" text-anchor="middle" font-size="14">Chat Interface • Model Management • Performance Monitoring</text>
  <!-- Processing Layer -->
  <rect x="100" y="220" width="360" height="200" fill="#ff6b6b" fill-opacity="0.15" stroke="#dc3545" stroke-width="2" rx="10"/>
  <text x="290" y="250" text-anchor="middle" font-size="18" font-weight="bold">LLM Inference</text>
  <!-- LLM Components -->
  <rect x="120" y="340" width="150" height="60" fill="#ff6b6b" fill-opacity="0.4" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="195" y="367" text-anchor="middle" font-size="14" font-weight="bold">LLaMA.rn</text>
  <text x="195" y="383" text-anchor="middle" font-size="12">GGUF Models</text>
  <rect x="290" y="340" width="150" height="60" fill="#ff6b6b" fill-opacity="0.4" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="365" y="367" text-anchor="middle" font-size="14" font-weight="bold">Context Engine</text>
  <text x="365" y="383" text-anchor="middle" font-size="12">RAG Integration</text>
  <rect x="120" y="270" width="320" height="60" fill="#ff6b6b" fill-opacity="0.25" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="280" y="295" text-anchor="middle" font-size="14" font-weight="bold">Generation Pipeline</text>
  <text x="280" y="315" text-anchor="middle" font-size="12">Token Streaming • Performance Metrics</text>
  <!-- RAG Processing Layer -->
  <rect x="530" y="220" width="380" height="200" fill="#4ecdc4" fill-opacity="0.15" stroke="#17a2b8" stroke-width="2" rx="10"/>
  <text x="720" y="250" text-anchor="middle" font-size="18" font-weight="bold">RAG Pipeline</text>
  <!-- RAG Components -->
  <rect x="550" y="340" width="100" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="600" y="367" text-anchor="middle" font-size="14" font-weight="bold">ONNX</text>
  <text x="600" y="383" text-anchor="middle" font-size="12">Embeddings</text>
  <rect x="670" y="340" width="100" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="720" y="367" text-anchor="middle" font-size="14" font-weight="bold">Tokenizer</text>
  <text x="720" y="383" text-anchor="middle" font-size="12">BERT-style</text>
  <rect x="790" y="340" width="100" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="840" y="367" text-anchor="middle" font-size="14" font-weight="bold">HNSW</text>
  <text x="840" y="383" text-anchor="middle" font-size="12">Vector Search</text>
  <rect x="550" y="270" width="340" height="60" fill="#4ecdc4" fill-opacity="0.25" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="720" y="295" text-anchor="middle" font-size="14" font-weight="bold">Document Retrieval</text>
  <text x="720" y="315" text-anchor="middle" font-size="12">Chunked Metadata • LRU Cache • Relevance Ranking</text>
  <!-- Storage Layer -->
  <rect x="100" y="450" width="630" height="80" fill="#ffd93d" fill-opacity="0.2" stroke="#f39c12" stroke-width="2" rx="10"/>
  <text x="415" y="480" text-anchor="middle" font-size="18" font-weight="bold">Local Storage</text>
  <text x="415" y="505" text-anchor="middle" font-size="14">HNSW Index • Document Chunks • AI Models • Runtime Cache</text>
  <!-- Native Bridge -->
  <rect x="780" y="450" width="120" height="80" fill="#95a5a6" fill-opacity="0.3" stroke="#6c757d" stroke-width="2" rx="10"/>
  <text x="840" y="485" text-anchor="middle" font-size="14" font-weight="bold">Native Bridge</text>
  <text x="840" y="501" text-anchor="middle" font-size="12">Rust HNSW</text>
  <!-- Data Flow Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  <!-- Main Data Flow -->
  <!-- User Input -->
  <path d="M 205 190 L 205 220" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="105" y="210" font-size="14" font-weight="bold">1. User Query</text>
  <!-- Query to RAG -->
  <path d="M 460 300 L 530 300" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="465" y="285" font-size="14" font-weight="bold">2. Process</text>
  <!-- Context Back -->
  <path d="M 530 350 L 460 350" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="465" y="375" font-size="14" font-weight="bold">3. Context</text>
  <!-- Storage Access -->
  <path d="M 660 420 L 660 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 195 420 L 195 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 840 420 L 840 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 780 490 L 730 490" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- Response Flow -->
  <path d="M 375 220 L 375 190" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="390" y="210" font-size="14" font-weight="bold">4. Response</text>
  <!-- Key Benefits -->
  <rect x="425" y="570" width="150" height="50" fill="#e8f5e8" stroke="#28a745" stroke-width="1" rx="5"/>
  <text x="500" y="600" text-anchor="middle" font-size="18" font-weight="bold">Internet</text>
</svg>